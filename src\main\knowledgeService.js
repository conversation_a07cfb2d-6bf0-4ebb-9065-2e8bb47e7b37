/**
 * 知识库服务 - 主进程
 * 参考demo/libsql实现，提供完整的数据库操作功能
 */
const { createClient } = require('@libsql/client');
const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');
const TurndownService = require('turndown');

// 导入核心模块
const parser = require('./knowledge/parser.js');
const splitter = require('./knowledge/splitter.js');

class KnowledgeService {
  constructor(config = null) {
    this.client = null;
    this.openai = null;
    this.initialized = false;
    // 使用传入的配置或默认配置
    this.config = config || this.getDefaultConfig();
  }

  /**
   * 获取默认配置（使用代理）
   */
  getDefaultConfig() {
    return {
      database: {
        url: 'file:knowledge.db',
        timeout: 30000
      },
      embedding: {
        // 使用代理服务器，不直接调用外部API
        useProxy: true,
        proxyEndpoint: '/api/embeddings',
        model: 'BAAI/bge-m3', // 统一使用bge-m3
        encoding_format: 'float'
      },
      document: {
        minSplitLength: 1500,
        maxSplitLength: 2000
      }
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    // 如果已初始化，需要重新初始化客户端
    if (this.initialized) {
      this.reinitializeClients();
    }
  }

  /**
   * 重新初始化客户端
   */
  async reinitializeClients() {
    try {
      // 重新初始化OpenAI客户端
      this.openai = new OpenAI({
        baseURL: this.config.embedding.baseURL,
        apiKey: this.config.embedding.apiKey
      });
      console.log('✅ 知识库服务客户端重新初始化成功');
    } catch (error) {
      console.error('❌ 知识库服务客户端重新初始化失败:', error);
    }
  }

  /**
   * 初始化知识库服务
   */
  async initialize() {
    try {
      // 初始化数据库客户端
      this.client = createClient({
        url: this.config.database.url,
        timeout: this.config.database.timeout
      });

      // 初始化OpenAI客户端
      this.openai = new OpenAI({
        baseURL: this.config.embedding.baseURL,
        apiKey: this.config.embedding.apiKey
      });

      // 创建数据库表结构
      await this.createTables();

      this.initialized = true;
      console.log('✅ 知识库服务初始化成功');
      return { success: true, message: '知识库初始化成功' };
    } catch (error) {
      console.error('❌ 知识库服务初始化失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 创建数据库表结构
   */
  async createTables() {
    try {
      await this.client.batch([
        `CREATE TABLE IF NOT EXISTS user_file (
          id INTEGER PRIMARY KEY,
          file_type INTEGER NOT NULL,
          file_name TEXT NOT NULL,
          file_path TEXT NOT NULL,
          source_file_path TEXT NOT NULL,
          file_preview TEXT NOT NULL,
          remark TEXT NOT NULL,
          file_size INTEGER DEFAULT 0,
          create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE TABLE IF NOT EXISTS user_file_embd (
          id INTEGER PRIMARY KEY,
          file_id INTEGER NOT NULL,
          file_content TEXT NOT NULL,
          embedding F32_BLOB(1024),
          create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE INDEX IF NOT EXISTS file_embedding_idx 
         ON user_file_embd(libsql_vector_idx(embedding))`
      ], 'write');

      console.log('✅ 数据库表创建成功');
    } catch (error) {
      console.error('❌ 数据库表创建失败:', error);
      throw error;
    }
  }

  /**
   * 获取文档内容
   */
  async getDocumentContent(filePath) {
    try {
      const fileName = path.basename(filePath);
      let fileContent = '';

      if (fileName.toLowerCase().endsWith('.docx') || fileName.toLowerCase().endsWith('.doc')) {
        // 处理Word文档 - 优化表格处理
        const htmlResult = await mammoth.convertToHtml(
          { path: filePath },
          {
            // 优化表格转换选项
            styleMap: [
              // 保留表格结构
              "p[style-name='Table Grid'] => table > tr > td:fresh",
              "p[style-name='Table Normal'] => table > tr > td:fresh",
              // 保留标题样式
              "p[style-name='Heading 1'] => h1:fresh",
              "p[style-name='Heading 2'] => h2:fresh",
              "p[style-name='Heading 3'] => h3:fresh",
              "p[style-name='标题 1'] => h1:fresh",
              "p[style-name='标题 2'] => h2:fresh",
              "p[style-name='标题 3'] => h3:fresh"
            ],
            // 表格转换配置
            transformDocument: mammoth.transforms.paragraph(function (paragraph) {
              // 如果段落在表格中，保持原样
              return paragraph;
            }),
            convertImage: () => {
              return mammoth.docx.paragraph({
                children: [
                  mammoth.docx.textRun({
                    text: '[图片]'
                  })
                ]
              });
            },
            // 启用表格支持
            includeDefaultStyleMap: true
          }
        );

        console.log('Word转HTML结果长度:', htmlResult.value.length);
        console.log('Word转换消息:', htmlResult.messages);

        // 改进的HTML到Markdown转换
        const turndownService = new TurndownService({
          // 保留表格结构
          headingStyle: 'atx',
          codeBlockStyle: 'fenced',
          bulletListMarker: '-',
          // 表格处理规则
          rules: {
            // 自定义表格转换规则
            tableCell: {
              filter: ['th', 'td'],
              replacement: function (content, node) {
                return content.trim() + ' | ';
              }
            },
            tableRow: {
              filter: 'tr',
              replacement: function (content, node) {
                return '| ' + content + '\n';
              }
            },
            table: {
              filter: 'table',
              replacement: function (content, node) {
                // 获取表格的行
                const rows = content.trim().split('\n').filter(row => row.trim());
                if (rows.length === 0) return content;

                // 构建Markdown表格
                let markdownTable = '';

                // 第一行作为表头
                if (rows.length > 0) {
                  markdownTable += rows[0] + '\n';

                  // 添加分隔行
                  const headerCells = rows[0].split('|').length - 2; // 减去首尾的空格
                  const separator = '|' + ' --- |'.repeat(Math.max(headerCells, 1)) + '\n';
                  markdownTable += separator;

                  // 添加数据行
                  for (let i = 1; i < rows.length; i++) {
                    markdownTable += rows[i] + '\n';
                  }
                }

                return '\n\n' + markdownTable + '\n\n';
              }
            }
          }
        });

        // 添加表格支持
        turndownService.addRule('tableSupport', {
          filter: function (node) {
            return node.nodeName === 'TABLE';
          },
          replacement: function (content, node) {
            // 解析表格结构
            const rows = Array.from(node.querySelectorAll('tr'));
            if (rows.length === 0) return content;

            let markdownTable = '\n\n';

            rows.forEach((row, rowIndex) => {
              const cells = Array.from(row.querySelectorAll('td, th'));
              const cellContents = cells.map(cell => cell.textContent.trim().replace(/\|/g, '\\|'));

              markdownTable += '| ' + cellContents.join(' | ') + ' |\n';

              // 在第一行后添加分隔符
              if (rowIndex === 0) {
                const separator = '| ' + cellContents.map(() => '---').join(' | ') + ' |\n';
                markdownTable += separator;
              }
            });

            markdownTable += '\n\n';
            return markdownTable;
          }
        });

        fileContent = turndownService.turndown(htmlResult.value);

        // 后处理：清理和优化
        fileContent = this.postProcessMarkdown(fileContent);

      } else {
        // 处理其他文本文件
        fileContent = await fs.promises.readFile(filePath, 'utf8');
      }

      console.log(`文档内容获取完成: ${fileName}, 长度: ${fileContent.length}`);
      return fileContent;
    } catch (error) {
      console.error(`获取文档内容失败: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * 后处理Markdown内容
   */
  postProcessMarkdown(content) {
    // 清理多余的空行
    content = content.replace(/\n{3,}/g, '\n\n');

    // 修复表格格式
    content = content.replace(/\|\s*\|\s*/g, '| ');
    content = content.replace(/\|\s*$/gm, '|');
    content = content.replace(/^\s*\|/gm, '|');

    // 确保表格前后有空行
    content = content.replace(/([^\n])\n(\|.*\|)/g, '$1\n\n$2');
    content = content.replace(/(\|.*\|)\n([^\n|])/g, '$1\n\n$2');

    // 清理空的表格行
    content = content.replace(/\|\s*\|\s*\|\s*\n/g, '');

    return content.trim();
  }

  /**
   * 分割文档内容 - 优化表格处理
   */
  splitDocument(markdownText, minSplitLength = null, maxSplitLength = null) {
    const minLength = minSplitLength || this.config.document.minSplitLength;
    const maxLength = maxSplitLength || this.config.document.maxSplitLength;

    console.log(`📝 开始分割文档，最小长度: ${minLength}, 最大长度: ${maxLength}`);

    // 解析文档结构
    const outline = parser.extractOutline(markdownText);
    console.log(`📋 提取文档大纲: ${outline.length} 个标题`);

    // 检测和处理表格
    const processedContent = this.preprocessTables(markdownText);

    // 按标题分割文档
    const sections = parser.splitByHeadings(processedContent, outline);
    console.log(`📄 按标题分割: ${sections.length} 个段落`);

    // 处理段落，确保满足分割条件
    const result = splitter.processSections(sections, outline, minLength, maxLength);

    return result.map(r => ({
      summary: r.summary,
      content: r.content,
      formattedContent: `> **📑 摘要：** *${r.summary}*\n\n---\n\n${r.content}`
    }));
  }

  /**
   * 预处理表格内容
   */
  preprocessTables(content) {
    // 识别表格模式
    const tableRegex = /\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+((\|.*\|[\r\n]*)+)/g;

    let processedContent = content;
    let tableCount = 0;

    // 处理每个表格
    processedContent = processedContent.replace(tableRegex, (match, tableBody) => {
      tableCount++;
      console.log(`📊 处理第 ${tableCount} 个表格`);

      // 解析表格结构
      const lines = match.trim().split('\n');
      const headerLine = lines[0];
      const separatorLine = lines[1];
      const dataLines = lines.slice(2);

      // 提取表头
      const headers = headerLine.split('|')
        .map(h => h.trim())
        .filter(h => h.length > 0);

      console.log(`📊 表格列数: ${headers.length}, 数据行数: ${dataLines.length}`);

      // 如果表格很大，考虑按行分组
      if (dataLines.length > 10) {
        console.log(`📊 大表格检测，将进行分组处理`);

        // 按组分割大表格
        const groupSize = 8; // 每组最多8行
        let groupedTables = '';

        for (let i = 0; i < dataLines.length; i += groupSize) {
          const groupDataLines = dataLines.slice(i, i + groupSize);
          const groupTable = [
            headerLine,
            separatorLine,
            ...groupDataLines
          ].join('\n');

          const groupTitle = `### 表格第 ${Math.floor(i / groupSize) + 1} 组 (第 ${i + 1}-${Math.min(i + groupSize, dataLines.length)} 行)\n\n`;
          groupedTables += groupTitle + groupTable + '\n\n';
        }

        return `\n### 📊 ${this.generateTableTitle(headers)}\n\n${groupedTables}`;
      } else {
        // 小表格保持完整
        const tableTitle = `### 📊 ${this.generateTableTitle(headers)}\n\n`;
        return `\n${tableTitle}${match}\n\n`;
      }
    });

    if (tableCount > 0) {
      console.log(`📊 共处理 ${tableCount} 个表格`);
    }

    return processedContent;
  }

  /**
   * 生成表格标题
   */
  generateTableTitle(headers) {
    if (!headers || headers.length === 0) {
      return '数据表格';
    }

    // 根据表头内容智能生成标题
    const headerText = headers.slice(0, 3).join('、'); // 取前3个列名

    if (headers.includes('姓名') || headers.includes('员工') || headers.includes('人员')) {
      return `人员信息表 (${headerText}等)`;
    } else if (headers.includes('项目') || headers.includes('任务')) {
      return `项目信息表 (${headerText}等)`;
    } else if (headers.includes('产品') || headers.includes('服务')) {
      return `产品服务表 (${headerText}等)`;
    } else if (headers.includes('时间') || headers.includes('日期')) {
      return `时间数据表 (${headerText}等)`;
    } else {
      return `数据表格 (${headerText}等)`;
    }
  }

  /**
   * 获取嵌入向量（使用代理）
   */
  async getEmbedding(content) {
    try {
      console.log('🔗 正在通过代理获取嵌入向量，长度:', content.length);

      // 通过主进程API客户端调用代理服务
      const { createMainApiClient, getCurrentUserToken } = require('./main.js');
      const userToken = await getCurrentUserToken();
      // 向量化API需要使用完整的prod-api地址，不需要额外的/api后缀
      const client = createMainApiClient('http://114.67.112.88:9603/prod-api', userToken);
      
      const requestData = {
        model: this.config.embedding.model,
        input: content,
        encoding_format: this.config.embedding.encoding_format
      };

      const response = await client.post('/embeddings', requestData);
      
      // 打印完整的响应信息用于调试
      console.log('🔍 KnowledgeService向量化完整响应:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      })
      
      // 处理嵌套的响应结构
      const responseData = response.data.data || response.data;
      if (!responseData || !responseData[0] || !responseData[0].embedding) {
        throw new Error('代理返回的向量数据格式不正确');
      }

      const embedding = new Float32Array(responseData[0].embedding);
      console.log(`✅ 代理嵌入向量获取成功，维度: ${embedding.length}`);
      return embedding;
    } catch (error) {
      console.error('❌ 代理获取嵌入向量失败:', error);
      throw error;
    }
  }

  /**
   * 索引单个文档
   */
  async indexDocument(filePath, fileType = 1, remark = '文档') {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      // 获取文档内容
      const fileContent = await this.getDocumentContent(filePath);

      // 分割文档
      const chunks = this.splitDocument(fileContent);

      if (!chunks || chunks.length === 0) {
        return { success: false, error: '没有可索引的内容' };
      }

      const fileName = path.basename(filePath);
      const fileDir = path.dirname(filePath);
      const filePreview = chunks[0].content.substring(0, 200) + '...';

      // 获取文件大小
      let fileSize = 0;
      try {
        const fs = require('fs');
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          fileSize = stats.size;
        }
      } catch (error) {
        console.warn(`⚠️ 无法获取文件大小: ${filePath}`, error.message);
      }

      // 插入文件记录
      const result = await this.client.execute({
        sql: `INSERT INTO user_file (file_type, file_name, file_path, source_file_path, file_preview, remark, file_size) 
              VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id`,
        args: [fileType, fileName, fileDir, filePath, filePreview, remark, fileSize]
      });

      const fileId = result.rows[0].id;
      console.log(`✅ 文件记录插入成功，ID: ${fileId}`);

      // 插入文档片段和向量
      let successCount = 0;
      for (const chunk of chunks) {
        try {
          const embedding = await this.getEmbedding(chunk.content);

          await this.client.execute({
            sql: `INSERT INTO user_file_embd (file_id, file_content, embedding) VALUES (?, ?, ?)`,
            args: [fileId, chunk.content, embedding]
          });

          successCount++;
        } catch (error) {
          console.error('插入文档片段失败:', error);
        }
      }

      console.log(`✅ 文档索引完成: ${fileName}, 共处理 ${successCount}/${chunks.length} 个片段`);

      return {
        success: true,
        message: `文档索引完成`,
        fileId: fileId,
        chunksProcessed: successCount,
        totalChunks: chunks.length
      };
    } catch (error) {
      console.error('索引文档失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 使用重排序模型并过滤相似度低的片段（使用代理）
   */
  async rerank(similarChunks, queryText) {
    try {
      const documents = similarChunks.map(chunk => chunk.content);
      
      console.log('🔄 正在通过代理重排序文档片段:', {
        documentsCount: documents.length,
        queryLength: queryText.length
      });

      // 通过主进程API客户端调用代理服务
      const { createMainApiClient, getCurrentUserToken } = require('./main.js');
      const userToken = await getCurrentUserToken();
      const client = createMainApiClient(null, userToken);
      
      const requestData = {
        query: queryText,
        documents: documents,
        model: "BAAI/bge-reranker-v2-m3"
      };

      const response = await client.post('/rerank', requestData);
      
      // 打印完整的响应信息用于调试  
      console.log('🔍 KnowledgeService重排序完整响应:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      })
      
      // 处理嵌套的响应结构
      const responseData = response.data.data || response.data;
      if (responseData && responseData.results) {
        console.log(`✅ 代理重排序成功，返回 ${responseData.results.length} 个结果`);
        return this.getTopChunks(responseData, similarChunks);
      }
    } catch (err) {
      console.error('❌ 代理重排序失败:', err);
    }
    return similarChunks;
  }

  /**
   * 动态阈值筛选优质片段
   */
  async getTopChunks(response, chunks, topN = 4, minScore = 0.1) {
    // 提取并排序结果（按相关性分数降序）
    const sortedResults = response.results
      .slice() // 创建副本避免修改原数组
      .sort((a, b) => b.relevance_score - a.relevance_score);

    // 计算统计指标
    const scores = sortedResults.map(res => res.relevance_score);
    const mean = scores.reduce((sum, val) => sum + val, 0) / scores.length;
    const stdDev = Math.sqrt(
      scores.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / scores.length
    );

    // 改进的动态阈值计算
    // 使用更宽松的阈值：均值减去0.5个标准差，或者直接使用前N个结果
    const dynamicThreshold = Math.max(minScore, mean - 0.5 * stdDev);
    const finalThreshold = Math.min(dynamicThreshold, 0.05); // 设置上限，避免过于严格

    console.log(`📊 重排序统计: 均值=${mean.toFixed(3)}, 标准差=${stdDev.toFixed(3)}, 动态阈值=${finalThreshold.toFixed(3)}`);
    console.log(`📊 相关性分数分布:`, scores.map(s => s.toFixed(3)));

    // 如果动态阈值过滤掉太多结果，则直接返回前N个
    const filteredResults = sortedResults.filter(res => res.relevance_score >= finalThreshold);
    
    if (filteredResults.length === 0) {
      console.log(`⚠️ 动态阈值过滤掉所有结果，返回前${topN}个结果`);
      const indexList = sortedResults.slice(0, topN).map(res => res.index);
      return chunks.filter((chunk, index) => indexList.includes(index));
    }

    console.log(`📊 阈值过滤后剩余: ${filteredResults.length} 个结果`);
    
    // 筛选满足条件的chunks
    const indexList = filteredResults
      .slice(0, topN) // 限制最大返回数量
      .map(res => res.index);
      
    return chunks.filter((chunk, index) => indexList.includes(index));
  }

  /**
   * 根据文件类型查询相似片段
   */
  async findSimilarChunks(description, fileType = null, limit = 10) {
    const queryEmbedding = await this.getEmbedding(description);
    
    let sql, args;
    
    if (fileType) {
      // 查询指定fileType的file
      const files = await this.client.execute({
        sql: `SELECT id FROM user_file WHERE file_type = ?`,
        args: [fileType]
      });
      
      if (!files.rows.length) {
        return [];
      }
      
      const fileIds = files.rows.map(row => row.id);
      
      sql = `WITH vector_scores AS (
        SELECT rowid AS id,
               file_id,
               file_content,
               embedding,
               1 - vector_distance_cos(embedding, vector32(?)) AS similarity
        FROM user_file_embd
        WHERE file_id IN (${Array(fileIds.length).fill('?').join(',')})
        ORDER BY similarity DESC
        LIMIT ?
      )
      SELECT v.id,
             v.file_id AS fileId,
             v.file_content AS content,
             v.similarity,
             f.file_name,
             f.source_file_path AS filePath
      FROM vector_scores v
      LEFT JOIN user_file f ON v.file_id = f.id`;
      
      args = [JSON.stringify(Array.from(queryEmbedding)), ...fileIds, limit];
    } else {
      // 查询所有文件
      sql = `WITH vector_scores AS (
        SELECT rowid AS id,
               file_id,
               file_content,
               embedding,
               1 - vector_distance_cos(embedding, vector32(?)) AS similarity
        FROM user_file_embd
        ORDER BY similarity DESC
        LIMIT ?
      )
      SELECT v.id,
             v.file_id AS fileId,
             v.file_content AS content,
             v.similarity,
             f.file_name,
             f.source_file_path AS filePath
      FROM vector_scores v
      LEFT JOIN user_file f ON v.file_id = f.id`;
      
      args = [JSON.stringify(Array.from(queryEmbedding)), limit];
    }
    
    const results = await this.client.execute({ sql, args });
    return results.rows;
  }

  /**
   * 搜索知识库 - 增强版本，支持重排序和动态阈值
   */
  async searchKnowledge(query, limit = 4, fileType = null) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      console.log(`🔍 搜索知识库: "${query}"${fileType ? ` (文件类型: ${fileType})` : ''}`);

      // 第一步：使用向量搜索找到候选结果
      console.log('🔍 第一步：向量搜索候选结果...');
      let similarChunks = await this.findSimilarChunks(query, fileType, limit * 2);
      
      console.log(`🔍 向量搜索结果: ${similarChunks.length} 个候选`);
      
      if (similarChunks.length === 0) {
        console.log('⚠️ 向量搜索没有找到结果');
        return [];
      }

      // 第二步：使用重排序模型优化结果
      console.log('🔄 第二步：重排序优化结果...');
      const rerankedChunks = await this.rerank(similarChunks, query);
      
      console.log(`✨ 重排序后结果: ${rerankedChunks.length} 个`);

      // 转换为标准格式
      const searchResults = rerankedChunks.map(row => ({
        id: row.id,
        fileId: row.fileId || row.file_id,
        content: row.content,
        similarity: row.similarity,
        file_name: row.file_name,
        filePath: row.filePath || row.source_file_path
      }));

      // 输出详细的搜索结果信息
      searchResults.forEach((result, index) => {
        console.log(`  ${index + 1}. 相似度: ${(result.similarity * 100).toFixed(1)}%`);
        if (result.file_name) {
          console.log(`     来源文档: ${result.file_name}`);
        }
        console.log(`     内容预览: ${result.content.substring(0, 80)}...`);
      });

      console.log(`✅ 增强搜索完成，返回 ${searchResults.length} 个相关文档片段`);
      return searchResults;
    } catch (error) {
      console.error('搜索知识库失败:', error);
      return [];
    }
  }

  /**
   * 获取知识库统计信息
   */
  async getKnowledgeStats() {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const [fileCount, segmentCount] = await Promise.all([
        this.client.execute('SELECT COUNT(*) as count FROM user_file'),
        this.client.execute('SELECT COUNT(*) as count FROM user_file_embd')
      ]);

      return {
        totalFiles: fileCount.rows[0].count,
        totalSegments: segmentCount.rows[0].count
      };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return { totalFiles: 0, totalSegments: 0 };
    }
  }

  /**
   * 完全清空知识库 - 清空所有表、索引并重置
   */
  async clearKnowledgeBase() {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      console.log('🧹 开始完全清空知识库...');

      // 1. 删除向量索引
      try {
        await this.client.execute('DROP INDEX IF EXISTS file_embedding_idx');
        console.log('✅ 向量索引已删除');
      } catch (error) {
        console.warn('⚠️ 删除向量索引时出错:', error.message);
      }

      // 2. 清空所有数据表
      const tables = ['user_file_embd', 'user_file'];
      for (const table of tables) {
        try {
          const result = await this.client.execute(`DELETE FROM ${table}`);
          console.log(`✅ 表 ${table} 已清空，删除了 ${result.rowsAffected || 0} 条记录`);
        } catch (error) {
          console.warn(`⚠️ 清空表 ${table} 时出错:`, error.message);
        }
      }

      // 3. 重置自增ID
      try {
        await this.client.batch([
          'DELETE FROM sqlite_sequence WHERE name = "user_file"',
          'DELETE FROM sqlite_sequence WHERE name = "user_file_embd"'
        ], 'write');
        console.log('✅ 自增ID已重置');
      } catch (error) {
        console.warn('⚠️ 重置自增ID时出错:', error.message);
      }

      // 4. 重新创建向量索引
      try {
        await this.client.execute(
          'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
        );
        console.log('✅ 向量索引已重新创建');
      } catch (error) {
        console.warn('⚠️ 重新创建向量索引时出错:', error.message);
      }

      // 5. 执行VACUUM以回收空间
      try {
        await this.client.execute('VACUUM');
        console.log('✅ 数据库空间已回收');
      } catch (error) {
        console.warn('⚠️ 数据库VACUUM时出错:', error.message);
      }

      console.log('✅ 知识库完全清空完成');
      return { success: true, message: '知识库已完全清空并重置' };
    } catch (error) {
      console.error('清空知识库失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 删除文件及其相关片段
   */
  async deleteFile(fileId) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const embdResult = await this.client.execute({
        sql: 'DELETE FROM user_file_embd WHERE file_id = ?',
        args: [fileId]
      });

      const fileResult = await this.client.execute({
        sql: 'DELETE FROM user_file WHERE id = ?',
        args: [fileId]
      });

      return {
        success: true,
        deletedSegments: embdResult.rowsAffected,
        deletedFiles: fileResult.rowsAffected
      };
    } catch (error) {
      console.error('删除文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 列出所有文件
   */
  async listFiles(fileType = null, fileName = null, pageSize = 10, pageNum = 1) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      let baseSql = `SELECT
        id,
        file_type AS fileType,
        file_name AS fileName,
        file_path AS filePath,
        source_file_path AS sourceFilePath,
        file_preview AS filePreview,
        remark,
        create_time AS createTime
      FROM user_file WHERE 1 = 1`;

      let countSql = `SELECT COUNT(*) AS total FROM user_file WHERE 1 = 1`;
      let args = [];

      if (fileType) {
        baseSql += ` AND file_type = ?`;
        countSql += ` AND file_type = ?`;
        args.push(fileType);
      }

      if (fileName) {
        baseSql += ` AND file_name LIKE ?`;
        countSql += ` AND file_name LIKE ?`;
        args.push(`%${fileName}%`);
      }

      const offset = (pageNum - 1) * pageSize;
      args.push(pageSize, offset);

      const [countResult, dataResult] = await Promise.all([
        this.client.execute(countSql, args.slice(0, -2)),
        this.client.execute(baseSql + ` ORDER BY id DESC LIMIT ? OFFSET ?`, args)
      ]);

      return {
        total: countResult.rows[0]?.total || 0,
        rows: dataResult.rows,
        pagination: {
          pageSize,
          pageNum,
          totalPages: Math.ceil((countResult.rows[0]?.total || 0) / pageSize)
        }
      };
    } catch (error) {
      console.error('列出文件失败:', error);
      return { total: 0, rows: [], pagination: { pageSize, pageNum, totalPages: 0 } };
    }
  }
}

module.exports = KnowledgeService; 